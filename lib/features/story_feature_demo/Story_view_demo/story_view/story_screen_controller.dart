// import 'dart:developer';

// import 'package:flowkar/core/utils/navigator_service.dart';
// import 'package:flowkar/features/story_feature_demo/Story_view_demo/story_view/controller/story_controller.dart';
// import 'package:flowkar/features/story_feature_demo/Story_view_demo/story_view/models/story_model.dart';
// import 'package:flowkar/features/story_feature_demo/Story_view_demo/story_view/widgets/story_view.dart'
//  ;
// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// // import 'package:story_view_demo/story_view/controller/story_controller.dart';
// // import 'package:story_view_demo/story_view/story.dart';
// // import 'package:flowkar/features/story_view_demo/story_view/widgets/story_view.dart' as story_view;

// class StoryScreenController extends GetxController {
//   final List<NewStory> users;
//   final int initialIndex;
//   final PageController pageController;
//   final StoryController storyController = StoryController();
//   late List<List<StoryItem>> stories;

//   StoryScreenController(this.users, this.initialIndex, this.pageController) {
//     stories = users.map((user) {
//       return user.stories!.map((story) {
//         log("story: ${story.storyId}");
//         if (story.storytype == "video") {
//           return StoryItem.pageVideo(
//             story.storyfile ?? "",
//             controller: storyController,
//             duration: const Duration(seconds: 10),
//           );
//         } else {
//           return StoryItem.pageImage(
//             url: story.storyfile ?? "",
//             controller: storyController,
//             duration: const Duration(seconds: 3),
//           );
//         }
//       }).toList();
//     }).toList();

//   }

//   void onPageChange(int index) {
//     storyController.play();
//   }

//   void onStoryShow(StoryItem item) {
//     // Handle story show event if needed
//   }

//   void onPreviousUser() {
//     final currentPage = pageController.page?.round() ?? 0;
//     if (currentPage > 0) {
//       // If not on first user, go to previous user
//       pageController.previousPage(
//         duration: const Duration(milliseconds: 300),
//         curve: Curves.easeInOut,
//       );
//     }
//     // If on first user, do nothing (don't go back)
//   }

//   void onNext() {
//     final currentPage = pageController.page?.round() ?? 0;
//     if (currentPage < users.length - 1) {
//       // If not on last user, go to next user
//       pageController.nextPage(
//         duration: const Duration(milliseconds: 300),
//         curve: Curves.easeInOut,
//       );
//     } else {
//       // If on last user's last story, go back to previous screen
//       NavigatorService.goBack();
//     }
//   }

//   @override
//   void onClose() {
//     storyController.dispose();
//     pageController.dispose();
//     super.onClose();
//   }
// }

import 'package:flowkar/core/utils/navigator_service.dart';
import 'package:flowkar/features/story_feature_demo/Story_view_demo/story_view/controller/story_controller.dart';
import 'package:flowkar/features/story_feature_demo/Story_view_demo/story_view/models/story_model.dart';
import 'package:flowkar/features/story_feature_demo/Story_view_demo/story_view/widgets/story_view.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class StoryScreenController extends GetxController {
  final List<NewStory> users;
  final int initialIndex;
  final PageController pageController;
  final StoryController storyController = StoryController();
  late List<List<StoryItem>> stories;

  StoryScreenController(this.users, this.initialIndex, this.pageController) {
    stories = users.map((user) {
      return user.stories!.map((story) {
        StoryItem storyItem;
        if (story.storytype == "video") {
          storyItem = StoryItem.pageVideo(
            story.storyfile ?? "",
            controller: storyController,
            duration: const Duration(seconds: 10),
            // Add these missing parameters
            id: story.storyId ?? 0,
            story: user, // Pass the entire user story object
          );
        } else {
          storyItem = StoryItem.pageImage(
            url: story.storyfile ?? "",
            controller: storyController,
            duration: const Duration(seconds: 3),
            // Add these missing parameters
            id: story.storyId ?? 0,
            story: user, // Pass the entire user story object
          );
        }
        // Reset the shown state to ensure stories can be viewed properly
        storyItem.shown = false;
        return storyItem;
      }).toList();
    }).toList();
  }

  void onPageChange(int index) {
    // Reset all stories for the current user to unshown state
    if (index < stories.length) {
      for (var storyItem in stories[index]) {
        storyItem.shown = false;
      }
    }
    // Start playing from the beginning
    storyController.playFromStart();
  }

  void onStoryShow(StoryItem item) {
    // Handle story show event if needed
    // log("Currently showing story with ID: ${item.id}");
  }

  /// Reset all stories for a specific user to unshown state
  void resetUserStories(int userIndex) {
    if (userIndex < stories.length) {
      for (var storyItem in stories[userIndex]) {
        storyItem.shown = false;
      }
    }
  }

  /// Reset all stories for all users to unshown state
  void resetAllStories() {
    for (var userStories in stories) {
      for (var storyItem in userStories) {
        storyItem.shown = false;
      }
    }
  }

  void onPreviousUser() {
    final currentPage = pageController.page?.round() ?? 0;
    if (currentPage > 0) {
      // Reset stories for the previous user to unshown state
      final previousUserIndex = currentPage - 1;
      if (previousUserIndex < stories.length) {
        for (var storyItem in stories[previousUserIndex]) {
          storyItem.shown = false;
        }
      }
      // If not on first user, go to previous user
      pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
    // If on first user, do nothing (don't go back)
  }

  void onNext() {
    final currentPage = pageController.page?.round() ?? 0;
    if (currentPage < users.length - 1) {
      // Reset stories for the next user to unshown state
      final nextUserIndex = currentPage + 1;
      if (nextUserIndex < stories.length) {
        for (var storyItem in stories[nextUserIndex]) {
          storyItem.shown = false;
        }
      }
      // If not on last user, go to next user
      pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      // If on last user's last story, go back to previous screen
      NavigatorService.goBack();
    }
  }

  @override
  void onClose() {
    storyController.dispose();
    pageController.dispose();
    super.onClose();
  }
}
